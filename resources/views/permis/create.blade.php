@props(['unitatiFeroviare'])

<x-app-layout>
    <div class="flex mt-10">
        <div class="border rounded-lg flex-1 py-10 px-10">
            @if (session('message'))
                <div class="mb-5 text-center">
                    <p class="text-sm font-medium {{ session('showSecondForm') ? 'text-green-500' : 'text-red-500' }}">
                        {{ session('message') }}
                    </p>
                    @if (session('showSecondForm'))
                        <button id="create-permis"
                            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            Creează un permis
                        </button>
                    @endif

                </div>
            @endif
            <form id='first-form' class="max-w-sm mx-auto flex flex-col" action="{{ route('permise.check') }}"
                method="POST">
                @csrf
                <div class="mb-5">
                    <label for="permis-cnp"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">CNP</label>
                    <input type="text" id="permis-cnp" name='permis-cnp' value="{{ old('permis-cnp') }}"
                        maxlength="13"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <div id="cnp-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    @error('permis-cnp')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
                <div class="mb-5">
                    <label for="permis-nume"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nume</label>
                    <input type="text" id="permis-nume" name="permis-nume" value="{{ old('permis-nume') }}"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    @error('permis-nume')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
                <div class="mb-5">
                    <label for="permis-prenume"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Prenume</label>
                    <input type="text" id="permis-prenume" name="permis-prenume" value="{{ old('permis-prenume') }}"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    @error('permis-prenume')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>
                <button type="submit"
                    class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">Verifica</button>
            </form>

            @if (session('hasPermis'))
                <button type="button" id="butonTiparirePermis"
                    class="hidden text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">Tipărire
                    Permis</button>
                @if (session('columns') && session('dateTabel'))
                    <div class="relative overflow-x-auto shadow-md sm:rounded-lg mt-10">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead
                                class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-6 py-3 sticky">Action</th>
                                    @foreach (session('columns') as $column)
                                        <th scope="col" class="px-6 py-3">{{ $column }}</th>
                                    @endforeach
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (session('dateTabel') as $dataRow)
                                    <tr
                                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                        <td class="px-6 py-4 sticky">
                                            <input type="radio" name="row-radio" class="row-radio"
                                                data-row-id="{{ $dataRow->id }}">
                                        </td>
                                        @foreach (session('columns') as $column)
                                            <td class="px-6 py-4">{{ $dataRow->$column }}</td>
                                        @endforeach
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        <div class="mt-4">
                            {{ session('dateTabel')->links() }}
                        </div>
                    </div>
                @endif
            @endif

        </div>
        <div id='second-form-container' class="hidden border rounded-lg flex-1 py-10 px-10">
            <form id='second-form' class="flexmax-w-sm mx-auto" action="{{ route('permise.store') }}" method="POST">
                @csrf
                <div class="mb-5 flex flex-col items-start">

                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Unitate
                        feroviară</label>
                    <select id="unitati-feroviare" name='unitati-feroviare'
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">

                        @foreach ($unitatiFeroviare as $unitate)
                            <option value="{{ $unitate->firma }}">{{ $unitate->unitate }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="flex mb-10 inner-div flex-col">
                    <label for="loc-nastere" class="block mb-3 text-sm font-medium text-gray-900 dark:text-white">Locul
                        nașterii</label>
                    <input type="text" id="loc-nastere" name='loc-nastere'
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="mb-5">
                    <label for="limba-materna"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Limba
                        maternă</label>
                    <ul
                        class="items-center w-full text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg sm:flex dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <li class="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                            <div class="flex items-center ps-3">
                                <input id="limba-materna-1" type="radio" value="română" name="limba-materna"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500"
                                    checked>
                                <label for="limba-materna-1"
                                    class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Română</label>
                            </div>
                        </li>
                        <li class="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                            <div class="flex items-center ps-3">
                                <input id="limba-materna-2" type="radio" value="maghiară" name="limba-materna"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                <label for="limba-materna-2"
                                    class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Maghiară</label>
                            </div>
                        </li>
                        <li class="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                            <div class="flex items-center ps-3">
                                <input id="limba-materna-3" type="radio" value="germană" name="limba-materna"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                <label for="limba-materna-3"
                                    class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Germană</label>
                            </div>
                        </li>
                        <li class="w-full dark:border-gray-600">
                            <div class="flex items-center ps-3">
                                <input id="limba-materna-4" type="radio" value="alta" name="limba-materna"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-700 dark:focus:ring-offset-gray-700 focus:ring-2 dark:bg-gray-600 dark:border-gray-500">
                                <label for="limba-materna-4"
                                    class="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Altă
                                    limbă</label>
                            </div>
                        </li>

                    </ul>
                </div>

                <!-- Hidden input field for custom mother language -->
                <div id="custom-limba-materna" class="mb-5 hidden">
                    <label for="custom-limba-input"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Specificați limba
                        maternă</label>
                    <input type="text" id="custom-limba-input" name="custom-limba-materna"
                        placeholder="Introduceți limba maternă"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="mb-5">
                    <label for="nr-angajator"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Numărul
                        atribuit de
                        angajator</label>
                    <input type="text" id="nr-angajator" name='nr-angajator'
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                </div>
                <div class="mb-5">
                    <label for="judet" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Judet
                        domiciliu</label>
                    <select id="judet" name="judet"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="Alba">Alba</option>
                        <option value="Arad">Arad</option>
                        <option value="Argeș">Argeș</option>
                        <option value="Bacău">Bacău</option>
                        <option value="Bihor">Bihor</option>
                        <option value="Bistrița-Năsăud">Bistrița-Năsăud</option>
                        <option value="Botoșani">Botoșani</option>
                        <option value="Brașov">Brașov</option>
                        <option value="Brăila">Brăila</option>
                        <option value="Buzău">Buzău</option>
                        <option value="Caraș-Severin">Caraș-Severin</option>
                        <option value="Călărași">Călărași</option>
                        <option value="Cluj">Cluj</option>
                        <option value="Constanța">Constanța</option>
                        <option value="Covasna">Covasna</option>
                        <option value="Dâmbovița">Dâmbovița</option>
                        <option value="Dolj">Dolj</option>
                        <option value="Galați">Galați</option>
                        <option value="Giurgiu">Giurgiu</option>
                        <option value="Gorj">Gorj</option>
                        <option value="Harghita">Harghita</option>
                        <option value="Hunedoara">Hunedoara</option>
                        <option value="Ialomița">Ialomița</option>
                        <option value="Iași">Iași</option>
                        <option value="Ilfov">Ilfov</option>
                        <option value="Maramureș">Maramureș</option>
                        <option value="Mehedinți">Mehedinți</option>
                        <option value="Mureș">Mureș</option>
                        <option value="Neamț">Neamț</option>
                        <option value="Olt">Olt</option>
                        <option value="Prahova">Prahova</option>
                        <option value="Satu Mare">Satu Mare</option>
                        <option value="Sălaj">Sălaj</option>
                        <option value="Sibiu">Sibiu</option>
                        <option value="Suceava">Suceava</option>
                        <option value="Teleorman">Teleorman</option>
                        <option value="Timiș">Timiș</option>
                        <option value="Tulcea">Tulcea</option>
                        <option value="Vaslui">Vaslui</option>
                        <option value="Vâlcea">Vâlcea</option>
                        <option value="Vrancea">Vrancea</option>
                        <option value="București">București</option>
                        <option value="București Sector 1">București Sector 1</option>
                        <option value="București Sector 2">București Sector 2</option>
                        <option value="București Sector 3">București Sector 3</option>
                        <option value="București Sector 4">București Sector 4</option>
                        <option value="București Sector 5">București Sector 5</option>
                        <option value="București Sector 6">București Sector 6</option>
                    </select>
                </div>
                <div class="mb-5">
                    <label for="restrictii-medicale"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Restricții
                        medicale</label>

                    <div class="flex items-center mb-4">
                        <input type="checkbox" value="" id='restrictii-medicale-1'
                            name='restrictii-medicale-1'
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="restrictii-medicale-1"
                            class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Utilizarea obligatorie a
                            ochelarilor/lentilelor de contact</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" value="" id='restrictii-medicale-2'
                            name='restrictii-medicale-2'
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="restrictii-medicale-2"
                            class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Utilizarea obligatorie a
                            protezei auditive/dispozitivului ajutător pentru comunicare</label>
                    </div>

                </div>

                <button id = "submitBtn" type="submit"
                    class="text-white bg-green-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-green-700 dark:focus:ring-blue-800">Adaugă</button>
            </form>
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const createPermisButton = document.getElementById('create-permis');
            const btnTiparirePermis = document.getElementById('butonTiparirePermis');
            const radios = document.querySelectorAll('.row-radio');
            let selectedRowId = null;


            if (createPermisButton) {
                createPermisButton.addEventListener('click', function() {
                    document.getElementById('second-form-container').classList.remove('hidden');
                });
            }


            const updateButtonVisibility = () => {
                const anyChecked = Array.from(radios).some(radio => radio.checked);
                if (anyChecked) {
                    const radio = document.querySelector('.row-radio:checked');
                    selectedRowId = radio.getAttribute('data-row-id');

                    btnTiparirePermis.classList.remove('hidden');

                } else {
                    btnTiparirePermis.classList.add('hidden');

                }
            };

            radios.forEach(radio => {
                radio.addEventListener('change', function() {
                    updateButtonVisibility();
                });
            });

            btnTiparirePermis?.addEventListener('click', function() {
                if (selectedRowId) {
                    fetch(`/generatePermis/${selectedRowId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            console.log('Success:', data);
                            // Decode the base64 PDF content
                            const pdfContent = atob(data.pdf);
                            // Convert the decoded content to an array buffer
                            const arrayBuffer = new Uint8Array([...pdfContent].map(char => char
                                .charCodeAt(0))).buffer;
                            // Create a Blob from the array buffer
                            const pdfBlob = new Blob([arrayBuffer], {
                                type: 'application/pdf'
                            });
                            // Create a URL for the Blob
                            const pdfUrl = URL.createObjectURL(pdfBlob);
                            // Create a link element to trigger the download
                            const link = document.createElement('a');
                            link.href = pdfUrl;
                            link.download = 'permis.pdf';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                        })
                        .catch(error => {
                            console.error('Error:', error);
                        });
                }
            });

        });
        document.getElementById('id-in-tara').addEventListener('change', function() {
            var inputInTara = document.getElementById('input-id-tara');


            if (this.checked) {
                inputInTara.classList.remove('hidden');

            } else {
                inputInTara.classList.add('hidden');

            }
        });

        // Toggle custom mother language input
        const limbaMaterna4 = document.getElementById('limba-materna-4');
        const customLimbaMaterna = document.getElementById('custom-limba-materna');
        const customLimbaInput = document.getElementById('custom-limba-input');
        const limbaMaternaBtns = document.querySelectorAll('input[name="limba-materna"]');

        limbaMaternaBtns.forEach(function(radio) {
            radio.addEventListener('change', function() {
                if (this.value === 'alta' && this.checked) {
                    customLimbaMaterna.classList.remove('hidden');
                    customLimbaInput.focus(); // Focus on the input when shown
                } else {
                    customLimbaMaterna.classList.add('hidden');
                    customLimbaInput.value = ''; // Clear the input when hidden
                }
            });
        });


        
    </script>
</x-app-layout>
