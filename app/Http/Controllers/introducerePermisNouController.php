<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Permis;
use App\Models\UnitatiFeroviare;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;


// Include the CNP validation function
require_once app_path('Http/Controllers/solicitariController.php');

class introducerePermisNouController extends Controller
{

    public function index()
    {
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $unitatiFeroviare = UnitatiFeroviare::all();

        return view('permis.create', compact('unitatiFeroviare'));
    }

    /**
     * Check a resource is in storage.
     */
    public function check(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'permis-cnp' => 'required|string|size:13',
            'permis-nume' => 'required|string|max:255',
            'permis-prenume' => 'required|string|max:255',
        ]);
        Log::info('se face check');

        // Add custom CNP validation
        $validator->after(function ($validator) use ($request) {
            if (!valid_CNP($request->input('permis-cnp'))) {
                Log::info('CNP-ul introdus nu este valid.');
                $validator->errors()->add('permis-cnp', 'CNP-ul introdus nu este valid.');
            }
        });

        if ($validator->fails()) {
            return back()->withInput()->withErrors($validator);
        }

        Log::info('trece de validare');

        $columns = [
            'id',
            'motiv',
            'data_primire',
            'nume_titular',
            'prenume_titular',
            'sex',
            'data_nasterii',
            'locul_nasterii'
        ];
        $permiseExists = Permis::where('cnp', $request->input('permis-cnp'))->exists();
        Log::info('permiseExists' . $permiseExists);

        if ($permiseExists) {
            $dateTabel = Permis::select(...$columns)->where('cnp', $request->input('permis-cnp'))->paginate(10);
            return back()->with('message', 'Utilizatorul are deja un permis.')->with('hasPermis', true)->with('columns', $columns)->with('dateTabel', $dateTabel);
        } else {
            return back()->withInput()->with('message', 'Utilizatorul nu are permis, creează un permis.')
                ->with('showSecondForm', true);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'permis-cnp' => 'required|string|size:13',
            'permis-nume' => 'required|string|max:255',
            'permis-prenume' => 'required|string|max:255',
        ]);

        // Add custom CNP validation
        $validator->after(function ($validator) use ($request) {
            if (!valid_CNP($request->input('permis-cnp'))) {
                $validator->errors()->add('permis-cnp', 'CNP-ul introdus nu este valid.');
            }
        });

        if ($validator->fails()) {
            return back()->withInput()->withErrors($validator);
        }


        $exists = Permis::where('cnp', $request->input('permis-cnp'))->exists();

        if ($exists) {
            return back()->withInput()->with('message', 'Utilizatorul are deja un permis.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {


    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit()
    {
        return view('permis.edit');
    }

    public function search(Request $request)
    {
        $part1 = $request->input('part1');
        $part2 = $request->input('part2');
        $year = $request->input('year');
        $part4 = $request->input('part4');


        $searchTerm = $part1 . ' ' . $part2 . ' ' . $year . ' ' . $part4;

        $permis = Permis::where('numar_permis_NEI', $searchTerm)->first();

        $unitatiFeroviare = UnitatiFeroviare::all();

        return view('permis.edit', compact('permis'), compact('unitatiFeroviare'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
