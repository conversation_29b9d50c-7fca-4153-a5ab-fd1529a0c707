<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Solicitari;
use App\Models\Comisie;
use App\Models\Personal;
use App\Models\Permis;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\UnitatiFeroviare;
use Illuminate\Support\Facades\Log;

class permisController extends Controller
{
    // public function generatePDF($id)
    // {
    //     $id_permis = Permis::where('id', $id)->first(); 
    //     $prenume_titular = Permis::where('prenume_titular', $id_permis->prenume_titular)->first(); // coloana prenume titular must match the value of $id_permis->prenume_titular
    //     $nume_titular = Permis::where('nume_titular', $id_permis->nume_titular)->first();
    //     $data_nasterii = Permis::where('data_nasterii', $id_permis->data_nasterii)->first();
    //     $locul_nasterii = Permis::where('locul_nasterii', $id_permis->locul_nasterii)->first();
    //     $data_emitere = Permis::where('data_emitere', $id_permis->data_emitere)->first();
    //     $data_expirare = Permis::where('data_expirare', $id_permis->data_expirare)->first();
    //     $denumire_autoritate_emitenta = Permis::where('denumire_autoritate_emitenta', $id_permis->denumire_autoritate_emitenta)->first();
    //     $numar_atribuit_angajator = Permis::where('numar_atribuit_angajator', $id_permis->numar_atribuit_angajator)->first();
    //     $nr_permis = Permis::where('nr_permis', $id_permis->nr_permis)->first();
    //     // $fotografie_titular = Permis::where('fotografie_titular', $id_permis->fotografie_titular)->first();
    //     // $semnatura_titular = Permis::where('semnatura_titular', $id_permis->semnatura_titular)->first();
    //     $limba_materna = Permis::where('limba_materna', $id_permis->limba_materna)->first();





    //     $pdf = Pdf::loadView('permis', compact('id_permis', 'prenume_titular', 'nume_titular', 'data_nasterii', 'locul_nasterii', 'data_emitere', 'data_expirare', 'denumire_autoritate_emitenta', 'numar_atribuit_angajator', 'nr_permis',  'limba_materna'));

    //     return $pdf->download('permis.pdf');
    // }

    public function index(Request $request) {
        Log::info('A fost apelată metoda indexPermise.');
        $columns = [
            'id' => 'ID',
            'nr_permis' => 'Număr Permis',
            'nume_titular' => 'Nume',
            'prenume_titular' => 'Prenume',
            'data_emitere' => 'Dată Emitere',
            'cnp' => 'CNP',
            'judete' => 'Județ',
            // 'nr_card' => 'Număr Card',
            // 'motiv' => 'Motiv',
            // 'numar_permis_NEI' => 'Număr Permis NEI',
            // 'data_depunere' => 'Dată Depunere',
            // 'data_primire' => 'Dată Primire',
            // 'numar_identificare' => 'Număr Identificare',
            // 'strada_si_numar' => 'Adresă',
            // 'cod_postal' => 'Cod Poștal'
            // 'denumire_autoritate_emitenta',
            // 'statut_permis',
            // 'depus_solicitant',
            // 'depus_entitate',
            // 'angajator',
            // 'sex',
            // 'data_nasterii',
            // 'locul_nasterii',
            // 'nationalitate',
            // 'limba_materna',
            // 'tip_eliberare',
            // 'tara',
            // 'judet',
            // 'localitate',
            // 'telefon',
            // 'email',
            // 'fotografie',
            // 'data_primei_eliberari',
            // 'nr_intern_reg',
            // 'proces',
            // 'arhiva_docs',
            // 'nsaro',
            // 'data_expirare',
            // 'posta_de_trimitere',
            // 'numar_atribuit_angajator',
            // 'upload_cerere_semnat',
            // 'upload_caracter_personal',
            // 'upload_ciclu_invatamint',
            // 'upload_stare_fizica',
            // 'upload_stare_psihologica',
            // 'upload_competenta_profesionala',
            // 'upload_document_identitate',
            // 'numar_inregistrare_facultativ',
            // 'updated_at',
            // 'created_at',
            // 'operator',
            // 'inspector',
            // 'numar_bon',
            // 'printat',
            // 'vedere',
            // 'auditiv',
        ];

        $queryAfisarePermise = Permis::select(array_keys($columns))->orderBy('nr_permis', 'DESC');

        // fac căutare după nr_permis pe blade-ul de vizualizare
        // iau inputul din searchbar după key->search
        $search = $request->input('search');
        // verific ca acesta sa nu fie empty
        if (!empty($search)) {

            $permise = Permis::where('nr_permis', 'like', '%' . $search . '%')->get();
            if ($permise->isNotEmpty()) {
                
                Log::info('NR_PERMIS GASIT-> ' . $permise[0]->nr_permis);
                $queryAfisarePermise->where('nr_permis', 'like', '%' . $search . '%');
            }
        }

        $menus = config('customVariables.menus');


        $isAdmin = auth()->user()->hasRole('admin');
        $isSuperAdmin = auth()->user()->hasRole('super_admin');
        $isRegularUser = auth()->user()->hasRole('user');

        //querry-ul pentru a cauta permisele




        // returnez pagina pt rezultate
        $dateTabelPermise = $queryAfisarePermise->paginate(10);

        return view('permis.vizualizare', compact('dateTabelPermise', 'columns', 'menus', 'search'));
    }

    public function generatePDF($id)
    {
        $permis = Permis::find($id);

        if (!$permis) {
            return response()->json(['error' => 'Permis not found'], 404);
        }

        // Creează PDF-ul folosind view-ul 'permis' și variabilele necesare
        $pdf = Pdf::loadView('permis', [
            'prenume_titular' => $permis->prenume_titular,
            'nume_titular' => $permis->nume_titular,
            'data_nasterii' => $permis->data_nasterii,
            'locul_nasterii' => $permis->locul_nasterii,
            'data_emitere' => $permis->data_emitere,
            'data_expirare' => $permis->data_expirare,
            'denumire_autoritate_emitenta' => $permis->denumire_autoritate_emitenta,
            'numar_atribuit_angajator' => $permis->numar_atribuit_angajator,
            'nr_permis' => $permis->nr_permis,
            // 'fotografie_titular' => $permis->fotografie_titular,
            // 'semnatura_titular' => $permis->semnatura_titular,
            'limba_materna' => $permis->limba_materna,
        ]);

        $pdfContent = $pdf->output();

        return response()->json([
            'pdf' => base64_encode($pdfContent)
        ]);
    }
    public function create()
    {
        $unitatiFeroviare = UnitatiFeroviare::all();

        return view('permis.create', compact('unitatiFeroviare'));
    }

    /**
     * Check a resource is in storage.
     */
    public function check(Request $request)
    {
        $request->validate([
            'permis-cnp' => 'required|string|max:13',
            'permis-nume' => 'required|string|max:255',
            'permis-prenume' => 'required|string|max:255',
        ]);

        $columns = [
            'id',
            'motiv',
            'data_primire',
            'nume_titular',
            'prenume_titular',
            'sex',
            'data_nasterii',
            'locul_nasterii'
        ];
        $permiseExists = Permis::where('cnp', $request->input('permis-cnp'))->exists();

        if ($permiseExists) {
            $dateTabel = Permis::select(...$columns)->where('cnp', $request->input('permis-cnp'))->paginate(10);
            return back()->with('message', 'Utilizatorul are deja un permis.')->with('hasPermis', true)->with('columns', $columns)->with('dateTabel', $dateTabel);
        } else {
            return back()->withInput()->with('message', 'Utilizatorul nu are permis, creează un permis.')
                ->with('showSecondForm', true);
        }
    }

    /**
     * Store a newly created resource in storage.
     */

     public function modificarePermis(Request $request) {
        Log::info('A fost apelată metoda acordarePermis.');
        $columns = [
            'id' => 'ID',
            'nr_permis' => 'Număr Permis',
            'nume_titular' => 'Nume',
            'prenume_titular' => 'Prenume',
            'data_emitere' => 'Dată Emitere',
            'cnp' => 'CNP',
            'judete' => 'Județ',
        ];


        $unitatiFeroviare = UnitatiFeroviare::all();

        $queryAfisarePermise = Permis::select(array_keys($columns))->orderBy('nr_permis', 'DESC');

        // fac căutare după nr_permis pe blade-ul de vizualizare
        // iau inputul din searchbar după key->search
        $search = $request->input('search');
        // verific ca acesta sa nu fie empty
        if (!empty($search)) {

            $permise = Permis::where('nr_permis', 'like', '%' . $search . '%')->get();
            if ($permise->isNotEmpty()) {
                
                Log::info('NR_PERMIS GASIT-> ' . $permise[0]->nr_permis);
                $queryAfisarePermise->where('nr_permis', 'like', '%' . $search . '%');
            }
        }

        $menus = config('customVariables.menus');


        $isAdmin = auth()->user()->hasRole('admin');
        $isSuperAdmin = auth()->user()->hasRole('super_admin');
        $isRegularUser = auth()->user()->hasRole('user');

        //querry-ul pentru a cauta permisele




        // returnez pagina pt rezultate
        $dateTabelPermise = $queryAfisarePermise->paginate(10);

        return view('permis.modificarePermis', compact('dateTabelPermise', 'columns', 'menus', 'search', 'unitatiFeroviare'));
    }

    public function permisNeacordat(Request $request) {
        
    }

    public function permisDublat(Request $request) {
        
    }

    public function cardNeacordat(Request $request) {
        
    }

    public function cardDublat(Request $request) {
        
    }
    public function store(Request $request)
    {
        $request->validate([
            'permis-cnp' => 'required|string|max:13',
            'permis-nume' => 'required|string|max:255',
            'permis-prenume' => 'required|string|max:255',
        ]);


        $exists = Permis::where('cnp', $request->input('permis-cnp'))->exists();

        if ($exists) {
            return back()->withInput()->with('message', 'Utilizatorul are deja un permis.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {


    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {

        $unitatiFeroviare = UnitatiFeroviare::all();
        $permis = Permis::findOrFail($id);
        
        return view('permis.edit', compact('permis', 'unitatiFeroviare'));
    }

    public function search(Request $request)
    {
        $part1 = $request->input('part1');
        $part2 = $request->input('part2');
        $year = $request->input('year');
        $part4 = $request->input('part4');


        $searchTerm = $part1 . ' ' . $part2 . ' ' . $year . ' ' . $part4;

        $permis = Permis::where('numar_permis_NEI', $searchTerm)->first();

        $unitatiFeroviare = UnitatiFeroviare::all();

        return view('permis.edit', compact('permis', ), compact('unitatiFeroviare'));
    }
}
// 1. Prenume titular
// 2. Nume titular
// 3. Data si locul nasterii titular
// 4a. Data eliberarii permisului(AN/LUNA/ZI)
// 4b. Data expirarii permisului (AN/LUNA/ZI)
// 4c. Numele autoritatii emitente
// 4d. Numar identificare angajat
// 5. Numar permis
// 6. Fotografie titular
// 7. Semnatura titular
// 8. Domiciliu titular(optional) ????????????????
// 9a.1 Limba materna
// 9a.2 Informatii suplimentare ????????????????
// 9b. Restrictii medicale ????????????????

