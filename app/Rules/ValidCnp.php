<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidCnp implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->isValidCnp($value)) {
            $fail('CNP-ul introdus nu este valid.');
        }
    }

    /**
     * Validate Romanian CNP (Personal Numerical Code)
     */
    private function isValidCnp(string $cnp): bool
    {
        // Remove any spaces or dashes
        $cnp = preg_replace('/[\s\-]/', '', $cnp);

        // Check if CNP has exactly 13 digits
        if (!preg_match('/^\d{13}$/', $cnp)) {
            return false;
        }

        // Extract components
        $sex = (int) $cnp[0];
        $year = (int) substr($cnp, 1, 2);
        $month = (int) substr($cnp, 3, 2);
        $day = (int) substr($cnp, 5, 2);
        $county = (int) substr($cnp, 7, 2);
        $sequence = (int) substr($cnp, 9, 3);

        // Validate sex digit (1-8)
        if ($sex < 1 || $sex > 8) {
            return false;
        }

        // Determine full year based on sex digit
        $fullYear = $this->getFullYear($sex, $year);

        // Validate date
        if (!$this->isValidDate($fullYear, $month, $day)) {
            return false;
        }

        // Validate county code (01-52, plus some special codes)
        if ($county < 1 || $county > 52) {
            return false;
        }

        // Validate sequence number (001-999)
        if ($sequence < 1 || $sequence > 999) {
            return false;
        }

        // Validate check digit
        return $this->validateCheckDigit($cnp);
    }

    /**
     * Get full year based on sex digit and year
     */
    private function getFullYear(int $sex, int $year): int
    {
        switch ($sex) {
            case 1:
            case 2:
                return 1900 + $year;
            case 3:
            case 4:
                return 1800 + $year;
            case 5:
            case 6:
                return 2000 + $year;
            case 7:
            case 8:
                // Foreign residents - assume 1900s for validation
                return 1900 + $year;
            default:
                return 1900 + $year;
        }
    }

    /**
     * Validate if the date is valid
     */
    private function isValidDate(int $year, int $month, int $day): bool
    {
        if ($month < 1 || $month > 12) {
            return false;
        }

        if ($day < 1 || $day > 31) {
            return false;
        }

        // Use PHP's checkdate function for more precise validation
        return checkdate($month, $day, $year);
    }

    /**
     * Validate the check digit using the CNP algorithm
     */
    private function validateCheckDigit(string $cnp): bool
    {
        $weights = [2, 7, 9, 1, 4, 6, 3, 5, 8, 2, 7, 9];
        $sum = 0;

        for ($i = 0; $i < 12; $i++) {
            $sum += (int) $cnp[$i] * $weights[$i];
        }

        $remainder = $sum % 11;
        $expectedCheckDigit = $remainder < 10 ? $remainder : 1;

        return (int) $cnp[12] === $expectedCheckDigit;
    }
}
